import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { Icon } from '@iconify/react'
import DefaultProfile from '../../../assets/images/profile/default-profile.png'
import FeedPost from './FeedPost'
import { getMyPosts, toggleLike, addComment, getPostComments, generatePostShareUrl } from '../../../services/feedServices'
import { toast } from 'react-toastify'

// Move ActionButton outside to prevent recreation on every render
const ActionButton = React.memo(({ icon, count, onClick, isLiked, isLast, buttonStyle, actionButtonStyle }) => {
  const buttonClass = useMemo(() =>
    `btn border ${isLiked ? 'text-danger' : 'text-muted'}`,
    [isLiked]
  );

  const buttonStyleMemo = useMemo(() =>
    isLast ? { ...actionButtonStyle, marginRight: 0 } : actionButtonStyle,
    [isLast, actionButtonStyle]
  );

  return (
    <button
      className={buttonClass}
      onClick={onClick}
      style={buttonStyleMemo}
    >
      <div className="d-flex align-items-center justify-content-center">
        <Icon icon={icon} style={{fontSize: '1.2rem'}} />
        {count && <span className="ms-1" style={{fontSize: '0.9rem'}}>{count}</span>}
      </div>
    </button>
  );
});

const MyFeed = () => {
  // Posts state
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [postingNewPost, setPostingNewPost] = useState(false);
  const [userProfile, setUserProfile] = useState(null);

  // Comments state
  const [newComment, setNewComment] = useState({});
  const [showComments, setShowComments] = useState({});
  const [postComments, setPostComments] = useState({}); // Store comments for each post
  const [commentsLoading, setCommentsLoading] = useState({}); // Loading state for comments
  const [showFullText, setShowFullText] = useState({}); // Track which posts show full text

  // Load my posts
  const loadMyPosts = useCallback(async (page = 1, append = false) => {
    try {
      if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await getMyPosts(page, 5);

      if (response.success) {
        const transformedPosts = response.data.posts.map(post => ({
          id: post.id,
          user: {
            name: post.user_name,
            avatar: post.user_avatar || DefaultProfile
          },
          content: post.description,
          media: post.media_url ? {
            type: post.media_type,
            url: post.media_url
          } : null,
          isLiked: post.is_liked_by_user === 1,
          likes: post.likes_count,
          commentsCount: post.comments_count,
          created_at: post.created_at
        }));

        if (append) {
          setPosts(prev => [...prev, ...transformedPosts]);
        } else {
          setPosts(transformedPosts);
          // Store user profile from first post
          if (transformedPosts.length > 0) {
            setUserProfile({
              name: transformedPosts[0].user.name,
              profile_pic_url: transformedPosts[0].user.avatar
            });
          }
        }

        setCurrentPage(page);
        setHasMore(response.data.pagination.has_more);
      } else {
        toast.error('Failed to load posts');
      }
    } catch (error) {
      console.error('Error loading posts:', error);
      toast.error('Failed to load posts');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, []);

  // Load initial posts
  useEffect(() => {
    loadMyPosts(1);
  }, [loadMyPosts]);

  // Infinite scroll handler
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = document.documentElement.clientHeight;
      
      // Check if user has scrolled to bottom (with 100px threshold)
      if (scrollTop + clientHeight >= scrollHeight - 100) {
        if (!loadingMore && hasMore) {
          loadMorePosts();
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [loadingMore, hasMore]);

  // Button styles
  // Memoized styles to prevent re-renders
  const buttonStyle = useMemo(() => ({
    backgroundColor: 'transparent',
    borderColor: '#dee2e6'
  }), []);

  const actionButtonStyle = useMemo(() => ({
    flex: 1,
    marginRight: '10px',
    ...buttonStyle
  }), [buttonStyle]);

  // Load comments for a specific post
  const loadPostComments = useCallback(async (postId) => {
    try {
      setCommentsLoading(prev => ({ ...prev, [postId]: true }));

      // Load all comments at once by setting a high page size
      const response = await getPostComments(postId, 1, 1000);

      if (response.success) {
        const newComments = response.data.comments.map(comment => ({
          id: comment.id,
          user: comment.user_name,
          avatar: comment.user_avatar || DefaultProfile,
          text: comment.comment,
          timestamp: new Date(comment.commented_at).toLocaleDateString()
        }));

        setPostComments(prev => ({
          ...prev,
          [postId]: newComments
        }));
      } else {
        toast.error('Failed to load comments');
      }
    } catch (error) {
      console.error('Error loading comments:', error);
      toast.error('Failed to load comments');
    } finally {
      setCommentsLoading(prev => ({ ...prev, [postId]: false }));
    }
  }, []);

  // Event handlers
  const handleLike = async (postId) => {
    try {
      const response = await toggleLike(postId);
      if (response.success) {
        setPosts(posts.map(post =>
          post.id === postId
            ? {
                ...post,
                isLiked: !post.isLiked,
                likes: post.isLiked ? post.likes - 1 : post.likes + 1
              }
            : post
        ));
      } else {
        toast.error('Failed to update like');
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      toast.error('Failed to update like');
    }
  };

  const handleComment = (postId) => {
    const isOpening = !showComments[postId];
    setShowComments(prev => ({ ...prev, [postId]: !prev[postId] }));

    // Load comments when opening comments section for the first time
    if (isOpening && !postComments[postId]) {
      loadPostComments(postId);
    }
  };

  const handleSubmitComment = async (postId) => {
    const commentText = newComment[postId];
    if (!commentText || !commentText.trim()) {
      toast.error('Please enter a comment');
      return;
    }

    if (commentText.length > 400) {
      toast.error('Comment cannot exceed 400 characters');
      return;
    }

    try {
      const response = await addComment(postId, commentText.trim());
      if (response.success) {
        // Update the post's comments count
        setPosts(posts.map(post =>
          post.id === postId
            ? { ...post, commentsCount: post.commentsCount + 1 }
            : post
        ));

        // Add the new comment to the comments list
        const newCommentObj = {
          id: response.data.comment.id,
          user: response.data.comment.user_name,
          avatar: response.data.comment.user_avatar || DefaultProfile,
          text: response.data.comment.comment,
          timestamp: 'Just now'
        };

        setPostComments(prev => ({
          ...prev,
          [postId]: [newCommentObj, ...(prev[postId] || [])]
        }));

        setNewComment(prev => ({ ...prev, [postId]: '' }));
        toast.success('Comment added successfully');
      } else {
        toast.error('Failed to add comment');
      }
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Failed to add comment');
    }
  };

  const handlePostSubmit = async (newPost) => {
    console.log('handlePostSubmit called with:', newPost);
    setPostingNewPost(true);

    // Simulate API delay and then refresh the feed
    setTimeout(async () => {
      try {
        await loadMyPosts(1); // Reload posts to include the new one
        toast.success('Post created successfully!');
      } finally {
        setPostingNewPost(false);
      }
    }, 2000); // 2 second delay
  };

  const loadMorePosts = useCallback(() => {
    if (!loadingMore && hasMore) {
      console.log('Loading more posts...', { currentPage: currentPage + 1, hasMore });
      loadMyPosts(currentPage + 1, true);
    }
  }, [loadMyPosts, loadingMore, hasMore, currentPage]);

  const handleShare = async (post) => {
    try {
      // Generate shareable URL for the post
      const response = await generatePostShareUrl(post.id);

      if (response.success) {
        const shareUrl = response.data.shareUrl;

        // Prepare share data
        const shareData = {
          title: `${post.user.name}'s Post`,
          text: post.content || 'Check out this post!',
          url: shareUrl
        };

        // Check if Web Share API is supported
        if (navigator.share) {
          await navigator.share(shareData);
          console.log('Shared successfully');
        } else {
          // Fallback for browsers that don't support Web Share API
          // Copy to clipboard
          await navigator.clipboard.writeText(shareUrl);
          toast.success('Post link copied to clipboard!');
        }
      } else {
        toast.error('Failed to generate share link');
      }
    } catch (error) {
      console.error('Error sharing post:', error);
      if (error.name !== 'AbortError') {
        toast.error('Failed to share post');
      }
    }
  };

  // Render functions
  const renderMedia = (media) => {
    if (!media) return null;

    const mediaStyle = { width: '100%', maxHeight: '400px' };

    if (media.type === 'image') {
      return <img src={media.url} className="img-fluid rounded" alt="Post media" style={{...mediaStyle, objectFit: 'cover'}} />;
    } else if (media.type === 'video') {
      return (
        <video className="img-fluid rounded" controls style={mediaStyle}>
          <source src={media.url} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      );
    }
    return null;
  };

  const renderPostContent = (content, post) => {
    if (!content) return null;

    const hasMedia = post.media && (post.media.type === 'image' || post.media.type === 'video');

    // For text-only posts, show full content
    if (!hasMedia) {
      return (
        <div>
          <p className="card-text mb-2">{content}</p>
        </div>
      );
    }

    // For posts with media, show truncated text with "Show more" option
    const shouldTruncate = content.length > 100;
    const isShowingFull = showFullText[post.id];
    const displayText = isShowingFull ? content : content.substring(0, 100) + (shouldTruncate ? '...' : '');

    return (
      <div>
        <p className="card-text mb-2">
          {displayText}
          {shouldTruncate && (
            <button
              className="btn btn-link p-0 ms-2 text-primary text-decoration-none"
              onClick={() => setShowFullText(prev => ({ ...prev, [post.id]: !isShowingFull }))}
            >
              {isShowingFull ? 'Show less' : 'Show more'}
            </button>
          )}
        </p>
      </div>
    );
  };

  // Memoized comment handlers to prevent re-renders
  const handleCommentChange = useCallback((postId, value) => {
    setNewComment(prev => ({ ...prev, [postId]: value }));
  }, []);

  const handleCommentKeyDown = useCallback((e, postId) => {
    if (e.key === 'Enter') {
      handleSubmitComment(postId);
    }
  }, [handleSubmitComment]);

  const handleCommentSubmitClick = useCallback((postId) => {
    handleSubmitComment(postId);
  }, [handleSubmitComment]);

  const renderComments = (post) => {
    if (!showComments[post.id]) return null;

    const comments = postComments[post.id] || [];
    const isLoading = commentsLoading[post.id];

    return (
      <div className="border-top pt-3 mt-3">
        <h6 className="mb-3">Comments ({post.commentsCount || 0})</h6>

        {/* Comment Input */}
        <div className="d-flex mb-3">
          <img src={userProfile?.profile_pic_url || DefaultProfile} className="rounded-circle me-2" alt="Profile" style={{width: '32px', height: '32px'}} />
          <div className="flex-grow-1">
            <input
              type="text"
              className="form-control"
              placeholder="Write a comment..."
              value={newComment[post.id] || ''}
              onChange={(e) => handleCommentChange(post.id, e.target.value)}
              onKeyDown={(e) => handleCommentKeyDown(e, post.id)}
              maxLength={400}
            />
            <div className="d-flex justify-content-end mt-1">
              <small className={(newComment[post.id] || '').length > 360 ? 'text-warning' : 'text-muted'}>
                {(newComment[post.id] || '').length}/400 characters
              </small>
            </div>
          </div>
          <button
            className="btn btn-primary btn-sm ms-2 w-auto"
            onClick={() => handleCommentSubmitClick(post.id)}
            disabled={!newComment[post.id] || !newComment[post.id].trim()}
          >
            <Icon icon="mdi:send" />
          </button>
        </div>

        {/* Comments Loading State */}
        {isLoading ? (
          <div className="text-center py-3">
            <div className="spinner-border spinner-border-sm" role="status">
              <span className="visually-hidden">Loading comments...</span>
            </div>
            <p className="mt-2 text-muted small">Loading comments...</p>
          </div>
        ) : (
          <>
            {/* Comments Container */}
            <div id={`comments-container-${post.id}`}>
              {/* Existing Comments */}
              {comments.map(comment => (
                <div key={comment.id} className="d-flex mb-2">
                  <img src={comment.avatar} className="rounded-circle me-2" alt={comment.user} style={{width: '32px', height: '32px'}} />
                  <div className="bg-light rounded p-2 flex-grow-1">
                    <div className="fw-bold">{comment.user}</div>
                    <div>{comment.text}</div>
                    <div className="text-muted small mt-1">{comment.timestamp}</div>
                  </div>
                </div>
              ))}


            </div>
          </>
        )}
      </div>
    );
  };



  return (
    <div className="container py-4">
      <div className="row justify-content-center">
        <div className="col-md-8">
                    {/* Feed Info */}
                    <div className="text-center mb-4">
            <div className="p-3 bg-light rounded">
              <p className="text-muted mb-0 small">
                This is your personal feed where you can see all your posts and updates. 
                Create new posts below and manage your content here.
              </p>
            </div>
          </div>

          {/* Profile Header */}
          <div className="d-flex justify-content-between align-items-center mb-4">
            <div></div>
            <div className="d-flex align-items-center">
              <div className="text-end me-3">
                <h5 className="mb-0">My Posts</h5>
                <small className="text-muted">Your personal posts and updates</small>
              </div>
              <img 
                src={userProfile?.profile_pic_url || DefaultProfile} 
                className="rounded-circle" 
                alt={userProfile?.name || "Profile"} 
                style={{width: '50px', height: '50px'}} 
              />
            </div>
          </div>

          {/* Create Post Component */}
          <FeedPost onPostSubmit={handlePostSubmit} userProfile={userProfile} />


          {/* New Post Loading State */}
          {postingNewPost && (
            <div className="card mb-4" style={{
              animation: 'fadeIn 0.5s ease-in-out',
              border: '2px dashed #007bff',
              backgroundColor: '#f8f9fa'
            }}>
              <div className="card-body text-center py-4">
                <div className="spinner-border text-primary mb-3" role="status">
                  <span className="visually-hidden">Creating post...</span>
                </div>
                <h6 className="text-primary mb-2">Creating your post...</h6>
                <p className="text-muted mb-0">Please wait while we process your content</p>
              </div>
            </div>
          )}

          {/* Loading State */}
          {loading ? (
            <div className="text-center py-4">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-2 text-muted">Loading your posts...</p>
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-4">
              <Icon icon="mdi:post-outline" style={{ fontSize: '3rem', color: '#6c757d' }} />
              <p className="mt-2 text-muted">No posts yet. Be the first to share something!</p>
            </div>
          ) : (
            <>
              {/* Posts Feed */}
              {posts.map((post) => (
              <div key={post.id} className="card mb-4">
                <div className="card-body">
                  {/* Post Header */}
                  <div className="d-flex align-items-center mb-3">
                    <img src={post.user.avatar} className="rounded-circle me-3" alt={post.user.name} style={{width: '40px', height: '40px'}} />
                    <div className="flex-grow-1">
                      <h6 className="mb-0">{post.user.name}</h6>
                      <small className="text-muted">{new Date(post.created_at).toLocaleDateString()}</small>
                    </div>
                  </div>

                  {/* Post Content */}
                  <div className="mb-3">
                    {renderPostContent(post.content, post)}
                    {renderMedia(post.media)}
                  </div>

                  {/* Action Buttons */}
                  <div className="d-flex justify-content-between">
                    <ActionButton
                      icon={post.isLiked ? "mdi:heart" : "mdi:heart-outline"}
                      count={post.likes}
                      onClick={() => handleLike(post.id)}
                      isLiked={post.isLiked}
                      buttonStyle={buttonStyle}
                      actionButtonStyle={actionButtonStyle}
                    />
                    <ActionButton
                      icon="mdi:comment-outline"
                      count={post.commentsCount || 0}
                      onClick={() => handleComment(post.id)}
                      buttonStyle={buttonStyle}
                      actionButtonStyle={actionButtonStyle}
                    />
                    <ActionButton
                      icon="mdi:share-variant-outline"
                      onClick={() => handleShare(post)}
                      isLast={true}
                      buttonStyle={buttonStyle}
                      actionButtonStyle={actionButtonStyle}
                    />
                  </div>

                  {/* Comments Section */}
                  {renderComments(post)}
                </div>
              </div>
              ))}

              {/* Loading More Posts Indicator */}
              {loadingMore && (
                <div className="text-center py-4">
                  <div className="spinner-border text-primary mb-2" role="status">
                    <span className="visually-hidden">Loading more posts...</span>
                  </div>
                  <p className="text-primary mb-0">Loading more posts...</p>
                </div>
              )}

              {!hasMore && posts.length > 0 && (
                <div className="text-center py-3">
                  <p className="text-muted">You've reached the end of your posts!</p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default MyFeed; 